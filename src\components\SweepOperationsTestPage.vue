<template>
  <div class="sweep-test-page fixed inset-0 z-50 bg-gray-50 flex flex-col">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Sweep Operations Test Suite</h1>
        <p class="text-gray-600 mt-1">Comprehensive testing for OpenCascade.js sweep operations and boolean geometry</p>
      </div>
      <button
        @click="closeTestPage"
        class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded transition-colors"
      >
        Close
      </button>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Left Panel - Test Controls -->
      <div class="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Test Categories</h2>

          <!-- Door Thickness Selector -->
          <div class="mt-4 p-3 bg-gray-50 rounded-lg">
            <label class="block text-sm font-medium text-gray-700 mb-2">Door Thickness</label>
            <select
              v-model="selectedThickness"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="12">12mm (Thin Door)</option>
              <option value="18">18mm (Standard Door)</option>
              <option value="20">20mm (Thick Door)</option>
              <option value="25">25mm (Extra Thick Door)</option>
              <option value="30">30mm (Very Thick Door)</option>
            </select>
            <div class="text-xs text-gray-500 mt-1">
              Selected: {{ selectedThickness }}mm thickness
            </div>
          </div>

          <!-- Shape Configuration Panel -->
          <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <h3 class="text-sm font-medium text-blue-900 mb-3">Custom Shape Configuration</h3>

            <!-- Shape Type -->
            <div class="mb-3">
              <label class="block text-xs font-medium text-gray-700 mb-1">Shape Type</label>
              <select
                v-model="shapeConfig.type"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="circle">Circle</option>
                <option value="rectangle">Rectangle</option>
                <option value="line">Line</option>
                <option value="polyline">Polyline</option>
                <option value="arc">Arc</option>
              </select>
            </div>

            <!-- Position -->
            <div class="mb-3">
              <label class="block text-xs font-medium text-gray-700 mb-1">Position (X, Y)</label>
              <div class="flex space-x-2">
                <input
                  v-model.number="shapeConfig.x"
                  type="number"
                  placeholder="X"
                  class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                <input
                  v-model.number="shapeConfig.y"
                  type="number"
                  placeholder="Y"
                  class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
              </div>
            </div>

            <!-- Size/Dimensions -->
            <div class="mb-3" v-if="shapeConfig.type === 'circle'">
              <label class="block text-xs font-medium text-gray-700 mb-1">Radius (mm)</label>
              <input
                v-model.number="shapeConfig.radius"
                type="number"
                min="1"
                max="50"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
            </div>

            <div class="mb-3" v-if="shapeConfig.type === 'rectangle'">
              <label class="block text-xs font-medium text-gray-700 mb-1">Size (Width × Height)</label>
              <div class="flex space-x-2">
                <input
                  v-model.number="shapeConfig.width"
                  type="number"
                  placeholder="Width"
                  class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                <input
                  v-model.number="shapeConfig.height"
                  type="number"
                  placeholder="Height"
                  class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
              </div>
            </div>

            <div class="mb-3" v-if="shapeConfig.type === 'line'">
              <label class="block text-xs font-medium text-gray-700 mb-1">End Point (X2, Y2)</label>
              <div class="flex space-x-2">
                <input
                  v-model.number="shapeConfig.x2"
                  type="number"
                  placeholder="X2"
                  class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                <input
                  v-model.number="shapeConfig.y2"
                  type="number"
                  placeholder="Y2"
                  class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
              </div>
            </div>

            <div class="mb-3" v-if="shapeConfig.type === 'arc'">
              <label class="block text-xs font-medium text-gray-700 mb-1">Arc Properties</label>
              <div class="space-y-2">
                <input
                  v-model.number="shapeConfig.radius"
                  type="number"
                  placeholder="Radius"
                  class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                <div class="flex space-x-2">
                  <input
                    v-model.number="shapeConfig.startAngle"
                    type="number"
                    placeholder="Start Angle"
                    class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                  <input
                    v-model.number="shapeConfig.endAngle"
                    type="number"
                    placeholder="End Angle"
                    class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>

            <!-- Tool Type -->
            <div class="mb-3">
              <label class="block text-xs font-medium text-gray-700 mb-1">Tool Type</label>
              <select
                v-model="shapeConfig.toolType"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="4MM">4MM Endmill</option>
                <option value="6MM">6MM Endmill</option>
                <option value="8MM">8MM Endmill</option>
                <option value="10MM">10MM Endmill</option>
                <option value="12MM">12MM Endmill</option>
                <option value="16MM">16MM Endmill</option>
                <option value="20MM">20MM Endmill</option>
                <option value="V90">V90 V-bit</option>
                <option value="V120">V120 V-bit</option>
                <option value="BALLNOSE6">6MM Ballnose</option>
                <option value="BALLNOSE8">8MM Ballnose</option>
              </select>
            </div>

            <!-- Operation Type -->
            <div class="mb-3">
              <label class="block text-xs font-medium text-gray-700 mb-1">Operation Type</label>
              <select
                v-model="shapeConfig.operationType"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="DRILL">Drilling (Point/Center)</option>
                <option value="GROOVE">Groove (Outline/Border)</option>
                <option value="POCKET">Pocket (Full Area)</option>
                <option value="PROFILE">Profile (Contour)</option>
                <option value="VCARVE">V-Carve (V-bit)</option>
              </select>
            </div>

            <!-- Face Selection -->
            <div class="mb-3">
              <label class="block text-xs font-medium text-gray-700 mb-1">Face</label>
              <select
                v-model="shapeConfig.face"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="top">Top Face (Z = 0)</option>
                <option value="bottom">Bottom Face (Z = -thickness)</option>
                <option value="custom">Custom Z Position</option>
              </select>
            </div>

            <!-- Starting Z Position -->
            <div class="mb-3" v-if="shapeConfig.face === 'custom'">
              <label class="block text-xs font-medium text-gray-700 mb-1">Starting Z Position (mm)</label>
              <input
                v-model.number="shapeConfig.startZ"
                type="number"
                :min="-selectedThickness"
                :max="0"
                step="0.5"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
              <div class="text-xs text-gray-500 mt-1">
                Z=0 is top surface, Z=-{{ selectedThickness }} is bottom surface
              </div>

              <!-- Z Position Presets -->
              <div class="flex flex-wrap gap-1 mt-2">
                <button
                  @click="shapeConfig.startZ = 0"
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border"
                >
                  Top (0)
                </button>
                <button
                  @click="shapeConfig.startZ = -selectedThickness / 4"
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border"
                >
                  25% (-{{ (selectedThickness / 4).toFixed(1) }})
                </button>
                <button
                  @click="shapeConfig.startZ = -selectedThickness / 2"
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border"
                >
                  Mid (-{{ (selectedThickness / 2).toFixed(1) }})
                </button>
                <button
                  @click="shapeConfig.startZ = -selectedThickness * 0.75"
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border"
                >
                  75% (-{{ (selectedThickness * 0.75).toFixed(1) }})
                </button>
                <button
                  @click="shapeConfig.startZ = -selectedThickness"
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded border"
                >
                  Bottom (-{{ selectedThickness }})
                </button>
              </div>
            </div>

            <!-- Depth -->
            <div class="mb-3">
              <label class="block text-xs font-medium text-gray-700 mb-1">Depth (mm)</label>
              <input
                v-model.number="shapeConfig.depth"
                type="number"
                min="0.5"
                :max="selectedThickness"
                step="0.5"
                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
              <div class="text-xs text-gray-500 mt-1">
                {{ ((shapeConfig.depth / selectedThickness) * 100).toFixed(1) }}% of door thickness
                <span v-if="shapeConfig.face === 'custom'">
                  (from Z={{ shapeConfig.startZ }} to Z={{ shapeConfig.startZ - shapeConfig.depth }})
                </span>
              </div>
            </div>

            <!-- Custom Test Button -->
            <button
              @click="runCustomShapeTest"
              :disabled="isRunning"
              class="w-full px-3 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded disabled:opacity-50 transition-colors"
            >
              🔧 Test Custom Shape
            </button>
          </div>
        </div>
        
        <div class="flex-1 overflow-y-auto p-4 space-y-4">
          <!-- Utility Actions -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Utilities</h3>
            <div class="space-y-2">
              <button
                @click="clearResults"
                class="w-full p-2 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
              >
                Clear All Results
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Results and Visualization -->
      <div class="flex-1 flex flex-col">
        <!-- Results Header -->
        <div class="bg-white border-b border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">Test Results</h2>
            <div class="flex items-center space-x-2">
              <span v-if="isRunning" class="text-sm text-blue-600">
                <span class="inline-block animate-spin mr-1">⚙️</span>
                {{ currentTest }}
              </span>
              <span v-else-if="lastTestResult" class="text-sm" :class="lastTestResult.success ? 'text-green-600' : 'text-red-600'">
                {{ lastTestResult.success ? '✅ Success' : '❌ Failed' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Results Content -->
        <div class="flex-1 flex">
          <!-- Console Output -->
          <div class="w-1/2 border-r border-gray-200 flex flex-col">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-200">
              <h3 class="font-medium text-gray-900">Console Output</h3>
            </div>
            <div class="flex-1 overflow-y-auto p-4 bg-gray-900 text-green-400 font-mono text-sm">
              <div v-for="(log, index) in consoleOutput" :key="index" class="mb-1">
                <span class="text-gray-500">[{{ log.timestamp }}]</span>
                <span :class="getLogClass(log.level)">{{ log.message }}</span>
              </div>
              <div v-if="consoleOutput.length === 0" class="text-gray-500">
                No output yet. Run a test to see results.
              </div>
            </div>
          </div>

          <!-- 3D Visualization -->
          <div class="w-1/2 flex flex-col">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
              <h3 class="font-medium text-gray-900">3D Visualization</h3>
              <div class="flex items-center space-x-2">
                <label class="flex items-center space-x-1 text-sm">
                  <input
                    type="checkbox"
                    v-model="wireframeMode"
                    @change="toggleWireframe"
                    class="rounded"
                  >
                  <span>Wireframe</span>
                </label>
                <label class="flex items-center space-x-1 text-sm">
                  <input
                    type="checkbox"
                    v-model="debugMode"
                    @change="toggleDebugMarkers"
                    class="rounded"
                  >
                  <span>Debug Markers</span>
                </label>
                <button
                  @click="resetCamera"
                  class="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded"
                >
                  Reset View
                </button>
              </div>
            </div>
            <div class="flex-1 relative">
              <div 
                ref="threeContainer" 
                class="w-full h-full bg-gray-800"
                :class="{ 'opacity-50': isRunning }"
              ></div>
              
              <!-- Loading Overlay -->
              <div v-if="isRunning" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                <div class="text-white text-center">
                  <div class="animate-spin text-4xl mb-2">⚙️</div>
                  <div>{{ processingStep || 'Processing...' }}</div>
                </div>
              </div>
              
              <!-- Empty State -->
              <div v-if="!modelUrl && !isRunning" class="absolute inset-0 flex items-center justify-center text-gray-500">
                <div class="text-center">
                  <div class="text-6xl mb-4">🔧</div>
                  <div>Run a test to see 3D results</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from '../utils/OrbitControls'

// Define emits
const emit = defineEmits<{
  close: []
}>()

// Function to close the test page
const closeTestPage = () => {
  emit('close')
}

// Test state
const isRunning = ref(false)
const currentTest = ref('')
const lastTestResult = ref<{ success: boolean; message: string } | null>(null)
const testResults = ref<Map<string, { success: boolean; message: string; timestamp: string }>>(new Map())

// Door thickness selection
const selectedThickness = ref<number>(18) // Default to 18mm

// Shape configuration
const shapeConfig = ref({
  type: 'circle',
  x: 100,
  y: 75,
  radius: 15,
  width: 50,
  height: 30,
  x2: 150,
  y2: 75,
  startAngle: 0,
  endAngle: 180,
  toolType: '8MM',
  operationType: 'POCKET',
  depth: 5,
  face: 'top',
  startZ: 0
})

// Console output
interface LogEntry {
  timestamp: string
  level: 'info' | 'success' | 'error' | 'warning'
  message: string
}
const consoleOutput = ref<LogEntry[]>([])

// 3D Visualization
const threeContainer = ref<HTMLElement | null>(null)
const modelUrl = ref<string | null>(null)
const processingStep = ref<string>('')
const wireframeMode = ref<boolean>(false)
const debugMode = ref<boolean>(false)

// Three.js objects
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let controls: OrbitControls | null = null
let model: THREE.Object3D | null = null
let debugMarkers: THREE.Object3D[] = []

// OCJS Worker
let worker: Worker | null = null
const pendingMessages = new Map<string, { resolve: Function; reject: Function }>()

// Test definitions (removed - only custom shape test remains)

// Helper functions
function addLog(level: 'info' | 'success' | 'error' | 'warning', message: string) {
  const entry: LogEntry = {
    timestamp: new Date().toLocaleTimeString(),
    level,
    message
  }
  consoleOutput.value.push(entry)

  // Auto-scroll to bottom
  nextTick(() => {
    const consoleEl = document.querySelector('.overflow-y-auto')
    if (consoleEl) {
      consoleEl.scrollTop = consoleEl.scrollHeight
    }
  }, 10)
}

function getLogClass(level: string): string {
  switch (level) {
    case 'success': return 'text-green-400'
    case 'error': return 'text-red-400'
    case 'warning': return 'text-yellow-400'
    default: return 'text-green-400'
  }
}

function getTestButtonClass(testId: string): string {
  const result = testResults.value.get(testId)
  if (!result) return 'border-gray-300'
  return result.success ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'
}

onMounted(() => {
  initializeWorker()
  initThreeJS()
})

onUnmounted(() => {
  cleanup()
})

function initializeWorker() {
  try {
    worker = new Worker('/src/workers/ocjsWorker.ts', { type: 'module' })
    
    worker.onmessage = (event) => {
      const { id, type, data, error } = event.data
      const pending = pendingMessages.get(id)
      
      if (pending) {
        pendingMessages.delete(id)
        if (type === 'success') {
          pending.resolve(data)
        } else {
          pending.reject(new Error(error || 'Unknown worker error'))
        }
      }
    }

    worker.onerror = (error) => {
      addLog('error', `Worker error: ${error.message}`)
    }
    
    addLog('success', 'OCJS Worker initialized successfully')
  } catch (error) {
    addLog('error', `Failed to initialize worker: ${error}`)
  }
}

function initThreeJS(): boolean {
  if (!threeContainer.value) return false

  try {
    // Scene
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0xffffff) // White background

    // Camera
    camera = new THREE.PerspectiveCamera(75, threeContainer.value.clientWidth / threeContainer.value.clientHeight, 0.1, 1000)
    camera.position.set(150, 150, 150)

    // Renderer
    renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight)
    renderer.shadowMap.enabled = false // Disabled due to Three.js proxy issues
    renderer.setClearColor(0xffffff) // White background
    threeContainer.value.appendChild(renderer.domElement)

    // Controls
    controls = new OrbitControls(camera, renderer.domElement)
    // Note: enableDamping and dampingFactor are private in this version

    // Lighting setup for better model visibility
    const ambientLight = new THREE.AmbientLight(0x404040, 0.8)
    scene.add(ambientLight)

    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0)
    directionalLight1.position.set(100, 100, 50)
    scene.add(directionalLight1)

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.5)
    directionalLight2.position.set(-100, 50, -50)
    scene.add(directionalLight2)

    // Add coordinate axes for reference
    const axesHelper = new THREE.AxesHelper(50)
    scene.add(axesHelper)

    // Start render loop
    animate()
    
    addLog('success', 'Three.js initialized successfully')
    return true
  } catch (error) {
    addLog('error', `Failed to initialize Three.js: ${error}`)
    return false
  }
}

function animate() {
  if (!renderer || !scene || !camera || !controls) return
  
  requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

async function sendMessage(type: string, data: any): Promise<any> {
  if (!worker) throw new Error('Worker not initialized')
  
  const id = Math.random().toString(36).substring(2, 11)
  
  return new Promise((resolve, reject) => {
    pendingMessages.set(id, { resolve, reject })
    worker!.postMessage({ id, type, data })
  })
}



function getLogClass(level: string): string {
  switch (level) {
    case 'success': return 'text-green-400'
    case 'error': return 'text-red-400'
    case 'warning': return 'text-yellow-400'
    default: return 'text-green-400'
  }
}

function getTestButtonClass(testId: string): string {
  const result = testResults.value.get(testId)
  if (!result) return 'border-gray-300'
  return result.success ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'
}

// runTest function removed - only custom shape test remains
// Removed corrupted test content

async function runCustomShapeTest() {
  if (isRunning.value) return

  isRunning.value = true
  currentTest.value = 'Custom Shape Test'

  addLog('info', `🔧 Starting custom shape test`)
  addLog('info', `Shape: ${shapeConfig.value.type} | Tool: ${shapeConfig.value.toolType} | Operation: ${shapeConfig.value.operationType}`)
  addLog('info', `Position: (${shapeConfig.value.x}, ${shapeConfig.value.y}) | Face: ${shapeConfig.value.face} | Depth: ${shapeConfig.value.depth}mm`)
  if (shapeConfig.value.face === 'custom') {
    addLog('info', `Custom Z: Start=${shapeConfig.value.startZ}mm, End=${shapeConfig.value.startZ - shapeConfig.value.depth}mm`)
  }

  try {
    processingStep.value = 'Creating custom shape...'

    // Create door body
    const doorWidth = 200  // mm
    const doorHeight = 150 // mm

    const doorResult = await sendMessage('createDoorBody', {
      width: doorWidth / 1000,  // Convert mm to meters
      height: doorHeight / 1000, // Convert mm to meters
      thickness: selectedThickness.value / 1000 // Convert mm to meters
    })
    addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${selectedThickness.value}mm)`)

    // Generate layer name based on operation type
    let layerName = ''
    switch (shapeConfig.value.operationType) {
      case 'DRILL':
        layerName = `DRILL_${shapeConfig.value.toolType}`
        break
      case 'GROOVE':
        layerName = `K_${shapeConfig.value.toolType}_GROOVE`
        break
      case 'POCKET':
        layerName = `CEP_${shapeConfig.value.toolType}_POCKET`
        break
      case 'PROFILE':
        layerName = `H_${shapeConfig.value.toolType}_PROFILE`
        break
      case 'VCARVE':
        layerName = `V_${shapeConfig.value.toolType}_VCARVE`
        break
    }

    // Create positioned tool shape based on shape type
    processingStep.value = 'Creating positioned tool shape...'

    let command: any = {
      layer_name: layerName
    }

    // Configure command based on shape type (convert coordinates to meters and adjust for door center)
    // Door is centered at origin, so we need to offset coordinates by half door dimensions
    const doorCenterOffsetX = doorWidth / 2  // 100mm
    const doorCenterOffsetY = doorHeight / 2 // 75mm

    switch (shapeConfig.value.type) {
      case 'circle':
        command = {
          ...command,
          command_type: 'circle',
          x1: (shapeConfig.value.x - doorCenterOffsetX) / 1000,  // Adjust for door center, then convert to meters
          y1: (shapeConfig.value.y - doorCenterOffsetY) / 1000,  // Adjust for door center, then convert to meters
          radius: shapeConfig.value.operationType === 'DRILL' ? 0 : shapeConfig.value.radius / 1000 // Convert mm to meters
        }
        break

      case 'rectangle':
        command = {
          ...command,
          command_type: 'rectangle',
          x1: (shapeConfig.value.x - doorCenterOffsetX) / 1000,  // Adjust for door center, then convert to meters
          y1: (shapeConfig.value.y - doorCenterOffsetY) / 1000,  // Adjust for door center, then convert to meters
          x2: (shapeConfig.value.x + shapeConfig.value.width - doorCenterOffsetX) / 1000,  // Adjust for door center, then convert to meters
          y2: (shapeConfig.value.y + shapeConfig.value.height - doorCenterOffsetY) / 1000  // Adjust for door center, then convert to meters
        }
        break

      case 'line':
        command = {
          ...command,
          command_type: 'line',
          x1: (shapeConfig.value.x - doorCenterOffsetX) / 1000,   // Adjust for door center, then convert to meters
          y1: (shapeConfig.value.y - doorCenterOffsetY) / 1000,   // Adjust for door center, then convert to meters
          x2: (shapeConfig.value.x2 - doorCenterOffsetX) / 1000,  // Adjust for door center, then convert to meters
          y2: (shapeConfig.value.y2 - doorCenterOffsetY) / 1000   // Adjust for door center, then convert to meters
        }
        break

      case 'arc':
        command = {
          ...command,
          command_type: 'arc',
          x1: (shapeConfig.value.x - doorCenterOffsetX) / 1000,      // Adjust for door center, then convert to meters
          y1: (shapeConfig.value.y - doorCenterOffsetY) / 1000,      // Adjust for door center, then convert to meters
          radius: shapeConfig.value.radius / 1000,  // Convert mm to meters
          start_angle: shapeConfig.value.startAngle,
          end_angle: shapeConfig.value.endAngle
        }
        break

      case 'polyline':
        // Create a simple rectangular polyline for demo (adjust for door center, then convert to meters)
        command = {
          ...command,
          command_type: 'polyline',
          points: [
            { x: (shapeConfig.value.x - doorCenterOffsetX) / 1000, y: (shapeConfig.value.y - doorCenterOffsetY) / 1000 },
            { x: (shapeConfig.value.x + 40 - doorCenterOffsetX) / 1000, y: (shapeConfig.value.y - doorCenterOffsetY) / 1000 },
            { x: (shapeConfig.value.x + 40 - doorCenterOffsetX) / 1000, y: (shapeConfig.value.y + 30 - doorCenterOffsetY) / 1000 },
            { x: (shapeConfig.value.x - doorCenterOffsetX) / 1000, y: (shapeConfig.value.y + 30 - doorCenterOffsetY) / 1000 },
            { x: (shapeConfig.value.x - doorCenterOffsetX) / 1000, y: (shapeConfig.value.y - doorCenterOffsetY) / 1000 }
          ]
        }
        break
    }

    // Extract tool diameter from tool type
    const toolDiameter = parseInt(shapeConfig.value.toolType.replace(/[^0-9]/g, '')) || 8

    // Determine face and starting Z position
    let isBottomFace = false
    let startingZ = 0

    if (shapeConfig.value.face === 'bottom') {
      isBottomFace = true
      startingZ = -selectedThickness.value
    } else if (shapeConfig.value.face === 'custom') {
      isBottomFace = shapeConfig.value.startZ < (-selectedThickness.value / 2) // Bottom face if closer to bottom
      startingZ = shapeConfig.value.startZ
    } else {
      isBottomFace = false
      startingZ = 0
    }

    const toolResult = await sendMessage('createPositionedToolShapes', {
      tool: {
        name: `${shapeConfig.value.toolType}_CUSTOM`,
        type: shapeConfig.value.toolType.includes('V') ? 'vbit' : 'cylindrical',
        diameter: toolDiameter,
        shape: shapeConfig.value.toolType.includes('BALLNOSE') ? 'ballnose' : 'cylindrical'
      },
      commands: [command],
      depth: shapeConfig.value.depth / 1000, // Convert mm to meters
      isBottomFace: isBottomFace,
      startingZ: startingZ / 1000, // Convert mm to meters - Custom Z position
      doorWidth: doorWidth / 1000,  // Convert mm to meters
      doorHeight: doorHeight / 1000 // Convert mm to meters
    })

    addLog('success', `✅ Custom ${shapeConfig.value.type} tool created with ${shapeConfig.value.operationType} operation`)

    // Perform sweep operation
    processingStep.value = 'Performing custom sweep operation...'
    const sweepResult = await sendMessage('performSweepOperation', {
      doorBodyShape: doorResult.shapeId,
      toolGeometries: toolResult.shapeIds,
      operation: 'subtract'
    })

    addLog('success', `✅ Custom sweep operation completed: ${sweepResult.toolsProcessed} operations processed`)
    addLog('info', `📋 Custom test results:`)
    addLog('info', `📋 - Shape: ${shapeConfig.value.type} at (${shapeConfig.value.x}, ${shapeConfig.value.y})`)
    addLog('info', `📋 - Tool: ${shapeConfig.value.toolType} (${toolDiameter}mm diameter)`)
    addLog('info', `📋 - Operation: ${shapeConfig.value.operationType} (${layerName})`)
    addLog('info', `📋 - Face: ${shapeConfig.value.face} (${isBottomFace ? 'bottom' : 'top'} face processing)`)
    if (shapeConfig.value.face === 'custom') {
      addLog('info', `📋 - Z Position: Start=${startingZ}mm, End=${startingZ - shapeConfig.value.depth}mm`)
    }
    addLog('info', `📋 - Depth: ${shapeConfig.value.depth}mm (${((shapeConfig.value.depth / selectedThickness.value) * 100).toFixed(1)}% penetration)`)

    // Export and visualize
    processingStep.value = 'Exporting to GLB for visualization...'
    addLog('info', '📦 Exporting custom shape 3D model to GLB format...')
    const glbData = await sendMessage('exportGLB', sweepResult.shapeId)
    addLog('info', `📊 GLB file size: ${(glbData.byteLength / 1024).toFixed(1)} KB`)

    processingStep.value = 'Loading 3D visualization...'
    await loadModel(glbData)

    const result = { success: true, message: 'Custom shape test completed successfully', timestamp: new Date().toLocaleTimeString() }
    testResults.value.set('custom-shape', result)
    lastTestResult.value = result

    addLog('success', `✅ Custom shape test completed successfully`)

  } catch (error) {
    const result = { success: false, message: `Custom shape test failed: ${error}`, timestamp: new Date().toLocaleTimeString() }
    testResults.value.set('custom-shape', result)
    lastTestResult.value = result

    addLog('error', `❌ Custom shape test failed: ${error}`)
  } finally {
    isRunning.value = false
    currentTest.value = ''
    processingStep.value = ''
  }
}

function clearResults() {
  consoleOutput.value = []
  testResults.value.clear()
  lastTestResult.value = null

  if (model && scene) {
    scene.remove(model)
    model = null
  }

  addLog('info', '🧹 Results cleared')
}

async function loadModel(glbData: ArrayBuffer) {
  if (!scene) return

  addLog('info', `Loading 3D model (${glbData.byteLength} bytes)...`)

  // Clean up previous model
  if (model) {
    scene.remove(model)
  }

  // Create blob URL
  const blob = new Blob([glbData], { type: 'model/gltf-binary' })
  const url = URL.createObjectURL(blob)

  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }
  modelUrl.value = url

  // Load with GLTFLoader
  const loader = new GLTFLoader()
  return new Promise((resolve, reject) => {
    loader.load(
      url,
      (gltf) => {
        model = gltf.scene
        scene!.add(model)

        // Get model statistics
        let meshCount = 0
        let vertexCount = 0
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            meshCount++
            if (child.geometry) {
              const positions = child.geometry.attributes.position
              if (positions) {
                vertexCount += positions.count
              }
            }
          }
        })

        // Center the model
        const box = new THREE.Box3().setFromObject(model)
        const center = box.getCenter(new THREE.Vector3())
        model.position.sub(center)

        // Scale the model to fit the view
        const size = box.getSize(new THREE.Vector3())
        const maxDim = Math.max(size.x, size.y, size.z)
        if (maxDim > 0) {
          const scale = 200 / maxDim // Scale to fit in 200 units (larger scale)
          model.scale.setScalar(scale)
          addLog('info', `🔍 Model scaled by factor: ${scale.toFixed(3)}`)
        }

        // Add a bright material to make it more visible
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.material = new THREE.MeshLambertMaterial({
              color: 0x8B4513, // Brown color for wood
              wireframe: wireframeMode.value
            })
          }
        })

        addLog('success', `✅ 3D model loaded successfully`)
        addLog('info', `📊 Model stats: ${meshCount} meshes, ${vertexCount} vertices`)
        addLog('info', `📏 Model size: ${size.x.toFixed(1)} x ${size.y.toFixed(1)} x ${size.z.toFixed(1)} units`)
        resolve(gltf)
      },
      (progress) => {
        // Loading progress
        const percent = (progress.loaded / progress.total) * 100
        addLog('info', `Loading progress: ${percent.toFixed(1)}%`)
      },
      (error) => {
        addLog('error', `❌ Failed to load 3D model: ${error}`)
        reject(error)
      }
    )
  })
}



function toggleWireframe() {
  if (!model) return

  model.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      if (child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach(mat => {
            mat.wireframe = wireframeMode.value
          })
        } else {
          child.material.wireframe = wireframeMode.value
        }
      }
    }
  })

  addLog('info', `🔧 Wireframe mode: ${wireframeMode.value ? 'ON' : 'OFF'}`)
}

function resetCamera() {
  if (!camera || !controls) return

  // Reset camera position
  camera.position.set(150, 150, 150)
  camera.lookAt(0, 0, 0)

  // Reset controls
  controls.reset()

  addLog('info', '📷 Camera view reset')
}

function toggleDebugMarkers() {
  if (!scene) return

  if (debugMode.value) {
    // Add debug markers at tool positions
    addDebugMarkers()
  } else {
    // Remove debug markers
    clearDebugMarkers()
  }

  addLog('info', `🔍 Debug markers: ${debugMode.value ? 'ON' : 'OFF'}`)
}

function addDebugMarkers() {
  if (!scene) return

  // Clear existing markers
  clearDebugMarkers()

  // Tool positions from the console logs (converted to Three.js coordinates)
  const toolPositions = [
    { x: -0.04, y: 0.0125, z: -0.035, color: 0xff0000, size: 20, name: '8MM' }, // Red for 8MM - LARGE
    { x: 0.04, y: 0.0125, z: -0.035, color: 0x00ff00, size: 20, name: '6MM' },  // Green for 6MM - LARGE
    { x: 0.0, y: 0.0125, z: 0.025, color: 0x0000ff, size: 20, name: '4MM' },    // Blue for 4MM - LARGE
    // Add some reference markers that should definitely be visible
    { x: 0, y: 50, z: 0, color: 0xffff00, size: 30, name: 'ABOVE' },           // Yellow above door
    { x: 50, y: 0, z: 0, color: 0xff00ff, size: 30, name: 'RIGHT' },           // Magenta to the right
    { x: 0, y: 0, z: 50, color: 0x00ffff, size: 30, name: 'FRONT' }            // Cyan in front
  ]

  toolPositions.forEach((pos, index) => {
    // Create sphere geometry for the marker - MUCH LARGER
    const geometry = new THREE.SphereGeometry(pos.size, 16, 16) // Use size directly (no conversion)
    const material = new THREE.MeshBasicMaterial({
      color: pos.color,
      transparent: false, // Make them solid
      opacity: 1.0
    })
    const sphere = new THREE.Mesh(geometry, material)

    // Position the marker
    sphere.position.set(pos.x, pos.y, pos.z)

    // Add to scene - check if scene exists
    if (scene) {
      scene.add(sphere)
      debugMarkers.push(sphere)
    }

    const colorName = pos.color === 0xff0000 ? 'RED' :
                      pos.color === 0x00ff00 ? 'GREEN' :
                      pos.color === 0x0000ff ? 'BLUE' :
                      pos.color === 0xffff00 ? 'YELLOW' :
                      pos.color === 0xff00ff ? 'MAGENTA' :
                      pos.color === 0x00ffff ? 'CYAN' : 'UNKNOWN'
    addLog('info', `🎯 Debug marker ${index + 1}: ${colorName} ${pos.name} at (${pos.x}, ${pos.y}, ${pos.z})`)
  })

  addLog('success', `✅ Added ${debugMarkers.length} debug markers`)
}

function clearDebugMarkers() {
  if (!scene) return

  debugMarkers.forEach(marker => {
    scene!.remove(marker)
  })
  debugMarkers = []
}

function cleanup() {
  if (worker) {
    worker.terminate()
  }

  if (renderer && threeContainer.value) {
    threeContainer.value.removeChild(renderer.domElement)
    renderer.dispose()
  }

  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }
}
</script>

<style scoped>
.test-button {
  transition: all 0.2s ease;
}

.test-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-category {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.test-category:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
</style>
